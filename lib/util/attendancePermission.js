/**
 * Utility xử lý phân quyền cho hệ thống điểm danh
 * Logic phân quyền theo đơn vị và vai trò
 */

const _ = require('lodash');
const User = require('../models/user');
const Unit = require('../models/unit');

/**
 * Kiểm tra quyền tạo lịch làm việc
 * @param {String} creatorId - ID người tạo lịch
 * @param {Array} targetUserIds - Danh sách ID cán bộ được tạo lịch
 * @returns {Object} { canCreate: boolean, allowedUsers: Array, message: String }
 */
async function checkSchedulePermission(creatorId, targetUserIds) {
  try {
    // Lấy thông tin người tạo lịch
    const creator = await User.findById(creatorId)
      .populate('units')
      .populate('position')
      .lean();

    if (!creator) {
      return {
        canCreate: false,
        allowedUsers: [],
        message: '<PERSON>hô<PERSON> tìm thấy thông tin người tạo lịch'
      };
    }

    // Lấy thông tin các cán bộ được tạo lịch
    const targetUsers = await User.find({ _id: { $in: targetUserIds } })
      .populate('units')
      .lean();

    // Kiểm tra xem creator có thuộc tổ tổng hợp hoặc công an phường hồng bàng không
    const isGeneralUnit = await checkIsGeneralUnit(creator.units);
    const isPoliceStation = await checkIsPoliceStation(creator.units);

    let allowedUsers = [];
    let message = '';

    if (isGeneralUnit || isPoliceStation) {
      // Tổ tổng hợp hoặc công an phường hồng bàng: có thể tạo lịch cho tất cả
      allowedUsers = targetUsers.map(user => user._id.toString());
      message = 'Có quyền tạo lịch cho tất cả cán bộ';
    } else {
      // Các tổ khác: chỉ tạo lịch cho cán bộ trong cùng tổ
      const creatorUnitId = creator.units[creator.units.length - 1]._id.toString();

      allowedUsers = targetUsers
        .filter(user => {
          const userUnitId = user.units[user.units.length - 1]._id.toString();
          // Kiểm tra có unit chung không
          return userUnitId === creatorUnitId;
        })
        .map(user => user._id.toString());

      const deniedCount = targetUserIds.length - allowedUsers.length;
      if (deniedCount > 0) {
        if (allowedUsers.length > 0) {
          message = `Bạn chỉ có thể tạo lịch cho ${allowedUsers.length}/${targetUserIds.length} cán bộ. ${deniedCount} cán bộ không thuộc đơn vị quản lý.`;
        } else {
          message = `Cán bộ không thuộc đơn vị quản lý.`;
        }
      } else {
        message = 'Bạn chỉ có quyền tạo lịch cho các cán bộ trong đơn vị';
      }
    }

    return {
      canCreate: allowedUsers.length > 0,
      allowedUsers,
      message
    };

  } catch (error) {
    return {
      canCreate: false,
      allowedUsers: [],
      message: `Lỗi kiểm tra quyền: ${error.message}`
    };
  }
}

/**
 * Kiểm tra quyền xem thống kê điểm danh
 * @param {String} viewerId - ID người xem thống kê
 * @param {String} targetUserId - ID cán bộ được xem thống kê (optional)
 * @returns {Object} { canView: boolean, scope: String, message: String }
 */
async function checkStatisticsPermission(viewerId, targetUserId = null) {
  try {
    const viewer = await User.findById(viewerId)
      .populate('units')
      .lean();

    if (!viewer) {
      return {
        canView: false,
        scope: 'none',
        message: 'Không tìm thấy thông tin người xem'
      };
    }

    // Nếu xem thống kê của chính mình
    if (!targetUserId || targetUserId === viewerId) {
      return {
        canView: true,
        scope: 'personal',
        message: 'Có quyền xem thống kê cá nhân'
      };
    }

    // Kiểm tra đơn vị
    const isPoliceStation = await checkIsPoliceStation(viewer.units);

    if (isPoliceStation) {
      return {
        canView: true,
        scope: 'all',
        message: 'Có quyền xem thống kê tất cả cán bộ'
      };
    }

    // Kiểm tra cùng đơn vị
    const targetUser = await User.findById(targetUserId).populate('units').lean();
    if (!targetUser) {
      return {
        canView: false,
        scope: 'none',
        message: 'Không tìm thấy thông tin cán bộ được xem'
      };
    }

    const viewerUnitId = viewer.units[viewer.units.length - 1]._id.toString();
    const targetUnitId = targetUser.units[targetUser.units.length - 1]._id.toString();

    const hasSameUnit = viewerUnitId === targetUnitId;

    if (hasSameUnit) {
      return {
        canView: true,
        scope: 'unit',
        message: 'Có quyền xem thống kê cán bộ cùng đơn vị'
      };
    }

    return {
      canView: false,
      scope: 'none',
      message: 'Không có quyền xem thống kê cán bộ này'
    };

  } catch (error) {
    return {
      canView: false,
      scope: 'none',
      message: `Lỗi kiểm tra quyền: ${error.message}`
    };
  }
}

/**
 * Kiểm tra xem units có chứa tổ tổng hợp không
 * @param {Array} units - Danh sách units
 * @returns {Boolean}
 */
async function checkIsGeneralUnit(units) {
  if (!units || !units.length) return false;

  // Tìm unit có tên chứa "tổng hợp" (case insensitive)
  return units.some(unit =>
    unit.name && unit.name.toLowerCase().includes('tổng hợp')
  );
}

/**
 * Kiểm tra xem units có chứa công an phường hồng bàng không
 * @param {Array} units - Danh sách units
 * @returns {Boolean}
 */
async function checkIsPoliceStation(units) {
  if (!units || !units.length) return false;

  return units.length === 1;
}

/**
 * Lấy danh sách cán bộ mà user có quyền quản lý
 * @param {String} userId - ID người dùng
 * @returns {Array} Danh sách user IDs có thể quản lý
 */
async function getManagedUsers(userId) {
  try {
    const user = await User.findById(userId).populate('units').lean();
    if (!user) return [];

    const isGeneralUnit = await checkIsGeneralUnit(user.units);
    const isPoliceStation = await checkIsPoliceStation(user.units);

    if (isGeneralUnit || isPoliceStation) {
      // Có thể quản lý tất cả cán bộ
      const allUsers = await User.find({ status: 1 }).select('_id').lean();
      return allUsers.map(u => u._id.toString());
    }

    // Chỉ quản lý cán bộ cùng đơn vị
    const userUnitId = user.units[user.units.length - 1]._id.toString();
    const sameUnitUsers = await User.find({
      units: userUnitId,
      status: 1
    }).select('_id').lean();

    return sameUnitUsers.map(u => u._id.toString());

  } catch (error) {
    console.error('Error getting managed users:', error);
    return [];
  }
}

module.exports = {
  checkSchedulePermission,
  checkStatisticsPermission,
  checkIsGeneralUnit,
  checkIsPoliceStation,
  getManagedUsers
};